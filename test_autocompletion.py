#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from typing import List
from collections import Counter



# 给定 n 个非负整数，用来表示柱状图中各个柱子的高度。每个柱子彼此相邻，且宽度为 1 
# 求在该柱状图中，能够勾勒出来的矩形的最大面积




class Solution:
    def largestRectang(self, heights: List[int]) -> int:
        if not heights:
            return 0
        
        # 在数组前后各添加一个0作为哨兵
        # 前面的0保证栈初始不为空，后面的0保证所有元素都会被处理
        heights = [0] + heights + [0]
        
        # 初始化单调栈，存储的是柱子的索引位置
        # 栈中元素对应的柱子高度保持单调递增
        stack = [0]
        res = 0
        
        for i in range(1, len(heights)):
            # 当前柱子高度小于栈顶柱子高度时，开始计算面积
            while heights[i] < heights[stack[-1]]:
                # 弹出栈顶元素作为当前矩形的高度
                h = heights[stack.pop()]
                # 计算宽度：当前索引 - 新栈顶索引 - 1
                # 因为栈是单调递增的，所以宽度可以这样计算
                w = i - stack[-1] - 1
                # 更新最大面积
                res = max(res, h * w)
            
            # 将当前索引入栈，保持单调递增性质
            stack.append(i)

            print(stack)  # 打印栈变化过程(调试用)
        
        return res

if __name__ == "__main__":
    s = Solution()

    print(s.largestRectang([2,1,5,6,2,3]))  # 输出10
    print(s.largestRectang([2,4]))         # 输出4 
    print(s.largestRectang([2,1,2]))       # 输出3

